// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils from "../Game/GameUtils";
import LocalUtils from "../LocalUtils";
import UnitStateMachine from "../UnitComponent/UnitStateMachine";
import { WeaponInfo } from "../UnitComponent/Weapon";
import WeaponUtils from "../Weapons/WeaponUtils";
import GameDirector from "../Game/GameDirector";
import GameStuffManager from "../Game/GameStuffManager";
import { ResourceType } from "./Resource";
import StorageArea from "./StorageArea";
import Feature from "./Stuff";
import Unit from "./Unit";

const {ccclass, property} = cc._decorator;

@ccclass
export default class FloatFood extends Unit implements Feature {

    feature: Feature;

    @property
    isAutoBorn = false;

    // @property(cc.Node)
    // enemyNode = null;

    moveDir = cc.v2(1, 0);
    speed = 620;
    baseSpeed = 620;
    changeSpeedTime = 0.6;

    pickupDistance: number = 30;

    targetPos = cc.v2();
    targetPosOffset = cc.v2();
    lifeTime = 0;

    InitOnLoad() {
        this.unitNode = this.singlePrefabCreator.runningNode;
        super.InitOnLoad();

        let pos = cc.v2(this.node.x, this.node.y);
        this.node.setPosition(cc.v3());
        this.rootPosition = pos;

        this.ResetSpeed();

        this.ReInit();

        if(this.isAutoBorn) {
            this.OnBorn();
        }

        console.log(`this.feature: ${this.feature}`);
        
    }

    ReInit(): void {
        super.ReInit();
        this.focusUnit = GameUtils.mainHero.script;

        this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('DriftingMeat', (dt: number)=>{
            this.gameUpdate(dt);
        }, false, 2040);
    }

    Reset(pos: cc.Vec2) {
        super.Reset(pos);
        this.weaponList.forEach((e)=>{
            e.weaponScript.Reset();
        });
    }

    gameUpdate(dt: number) {
        if(this.isAlive) {
            this.MoveUpdate(dt);
            this.lifeTime += dt;
            
            let point = cc.v2(GameUtils.rootPathPoints.driftingMeatPathPoints[1].position);
            // 超过终点即消除
            if(this.rootPosition.x > GameUtils.rootPathPoints.driftingMeatPathPoints[3].position.x) {
                this.ReadyToDead();
            }
        } else {
            if(this.isAutoMoving) {
                if(this.autoMoveLeftTime >= 0) {
                    this.autoMoveLeftTime -= dt;
                    let pos = this.rootPosition.add(this.autoMoveDir.mul(this.autoMoveSpeed * dt));
                    this.rootPosition = pos;
                    this.body.y = this.autoMoveHeight * (1 - Math.abs((0.5 - (this.autoMoveAllTime - this.autoMoveLeftTime) / this.autoMoveAllTime) * 2));
                    // console.log(`id: ${this.unit_id}, this.body.y: ${this.body.y}`);
                }
                if(this.autoMoveLeftTime < 0) {
                    this.body.y = 0;
                    this.isAutoMoving = false;
                }
            }
        }
    }

    override LoadUnitStateMachine() {
    }

    EquipWeapon() {
    }

    ResetSpeed() {
        this.changeSpeedTime = 0.3 + Math.random() * 0.5;
        this.speed = (0.7 + Math.random() * 0.3) * this.baseSpeed;
        this.targetPosOffset = cc.v2(1.4, 1).mul(-1500 + Math.random() * 3000);
    }

    MoveUpdate(dt: number) {
        // 移动
        this.changeSpeedTime -= dt;
        if(this.changeSpeedTime <= 0) {
            this.ResetSpeed();
        }
        // let targetPos = this.focusUnit.rootPosition;
        this.MoveToTargetPos(dt, this.targetPos.add(this.targetPosOffset));
    }

    OnBorn() {
        super.OnBorn();
    }

    PickUp(targetStorageArea?: StorageArea) {
        if(!targetStorageArea) {
            targetStorageArea = GameUtils.mainHeroBackpack;
        }
        // console.log(`捡到水中的肉！`);
        GameStuffManager.instance.CreateDropResource(this.rootPosition, cc.v2(), ResourceType.meat, targetStorageArea, 0, false);
        this.ReadyToDead();
    }

    ReadyToDead() {
        this.isReadyToDead = true;
        this.unitNode.opacity = 0;
        this.node.active = false;
        GameManager.instance.LateFrameCall(()=>{
            this.OnDead();
        });
    }

    OnDead() {
        GameManager.instance.RemoveUpdateCallbackByID(this.gameUpdateCallbackId);
        this.node.destroy();
    }

    
    override MoveToTargetPos(dt: number, pos: cc.Vec2) {
        let moveDir = pos.sub(this.rootPosition).normalize();
        this.moveDir = moveDir;
        // let distance = moveDir.len();
        let distance = GameUtils.Fake3dDistanceExpand(pos, this.rootPosition);
        let moveDistance = this.speed * dt;
        let minDistance = this.minDistance;
        if(distance - moveDistance < minDistance) {
            moveDistance = distance - minDistance;
            if(moveDistance < 0) {
                moveDistance = 0;
            }
        } else {
        }
        let rb = this.rbNode.getComponent(cc.RigidBody);
        rb.linearDamping = 1;
        // rb.applyForceToCenter(moveDir.mul(this.speed), true);
        rb.applyLinearImpulse(moveDir.mul(moveDistance), rb.getWorldCenter(), true);
        // rb.applyForceToCenter(cc.v2(1, 0).mul(10000), true);
        // rb.applyLinearImpulse(moveDir.mul(this.speed), rb.getWorldCenter(), true);
        // rb.applyLinearImpulse(cc.v2(0, -1).mul(100000), rb.getWorldCenter(), true);
        // let newPos = this.rootPosition.add(this.moveDir.mul(moveDistance));
        // if(this.isAlive && moveDistance > 0.01) {
        //     // this.Move(newPos);
        //     this.rootPosition = newPos;
        // }
        // cc.director.getPhysicsManager().update();
        
        // console.log(`rbNode isActive: ${this.rbNode.active}, target pos: ${pos}, moveDir: ${moveDir}`);
        // console.log(`moveDir: ${moveDir.normalize()}, speed: ${this.speed}, this.rootPosition: ${this.rootPosition}`);
        
    }

    onDestroy(): void {
        super.onDestroy();
        // console.log('destroy!');
        // this.attribute.onDestroy();
        GameManager.instance.RemoveUpdateCallbackByID(this.gameUpdateCallbackId);
    }
}