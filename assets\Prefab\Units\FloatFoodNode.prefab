[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "FloatFoodNode", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 9, "groupIndex": 9, "_id": ""}, {"__type__": "f4792BXa6xO5p2/bY3bZx59", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "parent": {"__id__": 1}, "prefab": {"__uuid__": "11dfbb15-15a3-4776-92e0-0cb92d178d4b"}, "isSetOffset": false, "offset": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "08417J1tNlH6LNBgawqzVUw", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "tag": "floatFood", "model_id": 0, "singlePrefabCreator": {"__id__": 2}, "isAutoBorn": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bacJcbI1xKQZvq3/O2xpEC", "sync": false}]